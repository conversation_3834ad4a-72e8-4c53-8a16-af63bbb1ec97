import os
import sys

ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if ROOT_DIR not in sys.path: sys.path.append(ROOT_DIR)

import os
import pickle
import json
import sqlite3
import pandas as pd

from tqdm import tqdm
from typing import List, Dict, Tuple
from utils.llm import BaseEmbedding, ModelSelector

class BomDeal:
    def __init__(self,
                 db_path: str,
                 bom_path: str,
                 zilliz_path: str,
                 embedding_model: BaseEmbedding = None):
        '''
        初始化 BomDeal 类
        Args:
            db_path: 数据库路径 (用于存储处理后的 BOM json 文件, 过渡用)
            bom_path: Bom 路径 (路径下需要包含多个文件夹，每个文件夹下的 csv 文件中包含若干 BOM 数据)
            zilliz_path: zilliz 路径 (用于存储 zilliz 格式 BOM 数据)
        '''
        self.db_path = db_path
        self.bom_path = bom_path
        self.zilliz_path = zilliz_path

        # 检查路径是否都存在，不存在则创建(db_path 是一个文件，所以创建他的父目录即可)
        if not os.path.exists(os.path.dirname(self.db_path)):
            os.makedirs(os.path.dirname(self.db_path))
        if not os.path.exists(self.bom_path):
            os.makedirs(self.bom_path)
        if not os.path.exists(self.zilliz_path):
            os.makedirs(self.zilliz_path)

        self.embedding_model = embedding_model

        # 初始化 embedding 模型
        if embedding_model is None:
            self.embedding_model = BaseEmbedding(**ModelSelector.sc_qwen3_embedding_4b)

    # =============== 一、创建数据库，存储 BOM 原始数据 ===============
    def create_database(self) -> None:
        """
        创建 SQLite 数据库和表结构, 如果数据库不存在则创建
        Args:
            db_path: 数据库路径
        """
        # 创建数据库目录
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        # 创建数据库
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 创建表结构
        cursor.execute(f'''
            CREATE TABLE IF NOT EXISTS boms (
                primary_key TEXT PRIMARY KEY,
                vector BLOB,
                data TEXT,
            )
        ''')
        
        conn.commit()
        conn.close()

    def insert_boms(self, boms: List[Dict], batch_size: int = 1000) -> None:
        """
        批量插入 BOM 数据
        具体而言，会先检索所有 key 出来，然后找到现在 BOM 中有哪些 key 还没存进去再存（防止数据重复）
        """

        def prepare_bom_data(boms: List[Dict]) -> List[tuple]:
            """预处理 BOM 数据，返回适合批量插入的元组列表"""
            prepared_data = []
            for bom in boms:
                prepared_data.append((
                    bom['primary_key'],
                    None,  # embedding 初始为 None
                    bom['data']
                ))
            return prepared_data

        self.create_database()
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 获取已存在的 primary_key
        cursor.execute(f'SELECT primary_key FROM boms')
        existing_keys = set(row[0] for row in cursor.fetchall())

        # 预处理数据
        prepared_data = prepare_bom_data(boms)

        # 过滤掉已经存在的 primary_key
        filtered_data = [record for record in prepared_data if record[0] not in existing_keys]

        # 使用事务批量插入
        try:
            # 分批次插入
            for i in tqdm(range(0, len(filtered_data), batch_size)):
                batch = filtered_data[i:i + batch_size]
                cursor.executemany(f'''
                    INSERT OR REPLACE INTO boms (
                        primary_key, vector, data
                    ) VALUES (?, ?, ?)
                ''', batch)
                conn.commit()
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
    
    def main_create_insert(self):
        boms = []
        # 读取所有文件夹下的 csv 文件
        for folder in os.listdir(self.bom_path):
            folder_path = os.path.join(self.bom_path, folder)
            if os.path.isdir(folder_path):
                # 查找文件夹下的 csv 文件
                for file in os.listdir(folder_path):
                    if file.endswith('.csv'):
                        csv_path = os.path.join(folder_path, file)
                        # 读取 csv 文件并转换为 bom 数据
                        df = pd.read_csv(csv_path)
                        for index, row in df.iterrows():
                            # 将每一行转换为字典作为 data
                            data_dict = row.to_dict()
                            bom_item = {
                                'primary_key': f"{folder}_{index}",  # 使用文件夹名和行索引作为主键
                                'data': json.dumps(data_dict, ensure_ascii=False)  # 将整行数据转为 JSON 字符串
                            }
                            boms.append(bom_item)
        self.insert_boms(boms)

    # =============== 二、更新数据库中的 embedding =================
    def get_boms_without_embedding(self) -> List[Tuple[str, str]]:
        """获取数据库中没有 embedding 的 BOM 数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(f'''
            SELECT primary_key, data
            FROM boms
            WHERE vector IS NULL
        ''')

        boms = cursor.fetchall()
        conn.close()
        return boms

    def update_bom_embedding(self, bom_id: str, embedding: List[float]) -> None:
        """更新指定 BOM 数据的 embedding"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 将 embedding 列表转换为二进制数据
        embedding_blob = pickle.dumps(embedding)

        cursor.execute(f'''
            UPDATE boms
            SET vector = ?
            WHERE primary_key = ?
        ''', (embedding_blob, bom_id))

        conn.commit()
        conn.close()

    def batch_generate_embedding(self, boms: List[Tuple[str, str]],
                            batch_size: int = 16) -> Dict[str, List[float]]:
        """ 批量生成 embedding """
        bom_embeddings = {}

        for i in range(0, len(boms), batch_size):
            batch = boms[i:i + batch_size]
            # 使用 data 字段生成 embedding
            texts = [data for _, data in batch]

            # 生成 embedding
            embeddings = self.embedding_model.embedding(texts, vector_length=2048)

            # 将结果与 bom_id 对应存储
            for j, (bom_id, _) in enumerate(batch):
                if j < len(embeddings):  # 确保有对应的 embedding
                    bom_embeddings[bom_id] = embeddings[j]

        return bom_embeddings

    def process_boms_embedding(self, batch_size: int = 16) -> None:
        """处理数据库中所有没有 embedding 的 BOM 数据"""
        # 获取所有没有 embedding 的 BOM 数据
        boms = self.get_boms_without_embedding()
        print(f"找到 {len(boms)} 条需要处理的 BOM 数据")

        # 批量生成 embedding
        for i in tqdm(range(0, len(boms), batch_size)):
            batch = boms[i:i + batch_size]
            bom_embeddings = self.batch_generate_embedding(batch, batch_size)

            # 更新数据库
            for bom_id, embedding in bom_embeddings.items():
                self.update_bom_embedding(bom_id, embedding)

    # =============== 三、将数据库中数据转换为 zilliz 导入格式 ===============
    def get_boms_by_like(self, like_str: str = '%'):
        """根据 like 信息筛选数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 初始化搜索列名
        search_columns = ['primary_key', 'vector', 'data']

        cursor.execute(f'''
            SELECT {', '.join(search_columns)}
            FROM boms
            WHERE vector IS NOT NULL
            AND primary_key like '{like_str}'
        ''')

        boms = cursor.fetchall()
        conn.close()

        # 将搜索结果中每一个 bom 的 embedding 字段从二进制数据转换为 list 类型
        # 同时将 bom 转换为 map 类型，方便后续处理
        boms = [dict(zip(search_columns, bom)) for bom in boms]

        return boms

    def trans_to_zilliz(self, boms) -> list[dict]:
        # 转换为 zilliz 格式
        rows = []
        for bom in boms:
            try:
                # 获取向量表示
                # 这里的 bom['vector'] 是一个 BLOB 类型的二进制数据
                # 转换为 list 类型的向量

                # 构建数据字典
                # 根据 Milvus schema 构建数据字典
                data_dict = {
                    "primary_key": str(bom.get('primary_key', ''))[:1000],  # VARCHAR(1000)
                    "vector": pickle.loads(bom.get('vector')),  # FLOAT_VECTOR(2048)
                    "data": str(bom.get('data', ''))[:4900],  # VARCHAR(4900)
                }
                rows.append(data_dict)
            except Exception as e:
                print(f"处理文档 {bom.get('primary_key', '')} 时出错: {e}")
                continue

        return rows

    def main_trans_to_zilliz(self, folder_filter: str = '', save_type: str = 'all'):
        # 检查文件夹
        if not os.path.exists(self.zilliz_path):
            os.makedirs(self.zilliz_path)

        def save_to_json(rows: list[dict], file_name: str):
            if len(rows) == 0:
                print(f"没有数据，跳过保存")
                return
            path = f'{self.zilliz_path}/{file_name}'
            json_file = {'rows': rows}
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(json_file, f, ensure_ascii=False)
            print(f"数据已保存，共 {len(rows)} 条记录")

        # 根据文件夹过滤获取数据
        like_str = f'{folder_filter}%' if folder_filter else '%'
        boms = self.get_boms_by_like(like_str)
        if len(boms) == 0:
            print("没有找到符合条件的数据")
            return

        rows = self.trans_to_zilliz(boms)

        # 保存数据
        if save_type == 'all':
            save_to_json(rows, f'boms_all.json')
        else:
            save_to_json(rows, f'boms_{folder_filter}.json')


def deal_bom_data():
    # 1.2 初始化路径
    root_path_list = ['data']
    db_path = os.path.join(ROOT_DIR, *root_path_list, 'db', 'boms.db')
    bom_path = os.path.join(ROOT_DIR, *root_path_list, 'source', '模拟bom数据库-refine')
    zilliz_path = os.path.join(ROOT_DIR, *root_path_list, 'zilliz')

    # 1.3 初始化模型
    embedding_model=BaseEmbedding(**ModelSelector.sc_qwen3_embedding_4b) # 初始化 embedding 模型

    # 1.4 初始化 BomDeal 类
    bom_deal = BomDeal(year=2024,  # year 参数在新版本中不再使用
                           db_path=db_path,
                           bom_path=bom_path,
                           zilliz_path=zilliz_path,
                           # init model
                           embedding_model=embedding_model)

    # 3. 创建数据库
    bom_deal.create_database()

    # 4. 插入 BOM 数据
    print(f"1. 插入 BOM 数据到数据库中")
    bom_deal.main_create_insert()

    # 5. 更新 embedding  (可重复执行)
    print(f"2. 更新 embedding")
    bom_deal.process_boms_embedding(64)

    # 6. 转换为 zilliz 格式 (可重复执行)
    print(f"3. 转换为 zilliz 格式")
    bom_deal.main_trans_to_zilliz()


if __name__ == "__main__":
    pass