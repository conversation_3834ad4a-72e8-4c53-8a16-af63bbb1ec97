import os
import sys

ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if ROOT_DIR not in sys.path: sys.path.append(ROOT_DIR)

import os
import pickle
import json
import sqlite3

from tqdm import tqdm
from typing import List, Dict, Tuple
from llmlingua import PromptCompressor
from utils.llm import BaseEmbedding, ModelSelector

class BomDeal:
    def __init__(self,
                 year: int,
                 db_path: str,
                 bom_path: str,
                 zilliz_path: str,
                 embedding_model: BaseEmbedding = None):
        '''
        初始化 BomDeal 类
        Args:
            bom_path: Bom 路径 (路径下需要包含多个 json 文件，每个 json 文件包含若干论文)
            db_path: 数据库路径 (用于存储处理后的论文 json 文件, 过渡用)
            zilliz_path: zilliz 路径 (用于存储 zilliz 格式论文)
        '''
        self.year = year
        self.db_path = db_path
        self.bom_path = bom_path
        self.zilliz_path = zilliz_path

        # 检查路径是否都存在，不存在则创建(db_path 是一个文件，所以创建他的父目录即可)
        if not os.path.exists(os.path.dirname(self.db_path)):
            os.makedirs(os.path.dirname(self.db_path))
        if not os.path.exists(self.bom_path):
            os.makedirs(self.bom_path)
        if not os.path.exists(self.zilliz_path):
            os.makedirs(self.zilliz_path)

        self.embedding_model = embedding_model

        # 初始化 embedding 模型
        if embedding_model is None:
            self.embedding_model = BaseEmbedding(**ModelSelector.sc_qwen3_embedding_4b)

    # =============== 一、创建数据库，存储论文原始数据 ===============
    def create_database(self) -> None:
        """
        创建 SQLite 数据库和表结构, 如果数据库不存在则创建
        Args:
            db_path: 数据库路径
        """
        # 创建数据库目录
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        # 创建数据库
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 创建表结构
        cursor.execute(f'''
            CREATE TABLE IF NOT EXISTS papers (
                primary_key TEXT PRIMARY KEY,
                vector BLOB,
                title TEXT,
                authors TEXT,
                summary TEXT,
                published TEXT,
                category TEXT,
                pdf_url TEXT,
                sub_summary TEXT,
                source TEXT,
                doi TEXT
            )
        ''')
        
        conn.commit()
        conn.close()

    def insert_papers(self, papers: List[Dict], batch_size: int = 1000) -> None:
        """
        批量插入论文数据
        具体而言，会先检索所有 key 出来，然后找到现在论文中有哪些 key 还没存进去再存（防止数据重复）
        """

        def prepare_paper_data(papers: List[Dict]) -> List[tuple]:
            """预处理论文数据，返回适合批量插入的元组列表"""
            prepared_data = []
            for paper in papers:
                prepared_data.append((
                    paper['primary_key'],
                    None,  # embedding 初始为 None
                    paper['title'],
                    json.dumps(paper['authors']),
                    paper['summary'],
                    paper['published'],
                    paper['category'],
                    paper['pdf_url'],
                    None, # sub_summary 初始为 None
                    paper['source'],
                    paper['doi']
                ))
            return prepared_data

        self.create_database()
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 获取已存在的 primary_key
        cursor.execute(f'SELECT primary_key FROM papers')
        existing_keys = set(row[0] for row in cursor.fetchall())
        
        # 预处理数据
        prepared_data = prepare_paper_data(papers)

        # 过滤掉已经存在的 primary_key
        filtered_data = [record for record in prepared_data if record[0] not in existing_keys]

        # 使用事务批量插入
        try:
            # 分批次插入
            for i in tqdm(range(0, len(filtered_data), batch_size)):
                batch = filtered_data[i:i + batch_size]
                cursor.executemany(f'''
                    INSERT OR REPLACE INTO papers (
                        primary_key, vector, title, authors, summary, published, category, 
                        pdf_url, sub_summary, source, doi
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', batch)
                conn.commit()
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
    
    def main_create_insert(self):
        papers = []
        # 读取所有 json 文件
        for file in os.listdir(self.bom_path):
            if file.endswith('.json') and f'_{self.year}' in file:
                with open(f'{self.bom_path}/{file}', 'r', encoding='utf-8') as f:
                    papers.extend(json.load(f))
        self.insert_papers(papers)

    # =============== 二、更新数据库中的 embedding =================
    def get_papers_without_embedding(self) -> List[Tuple[str, str, str]]:
        """获取数据库中没有 embedding 的文章"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f'''
            SELECT primary_key, title, sub_summary
            FROM papers
            WHERE vector IS NULL
        ''')
        
        papers = cursor.fetchall()
        conn.close()
        return papers

    def update_paper_embedding(self, paper_id: str, embedding: List[float]) -> None:
        """更新指定文章的 embedding"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 将 embedding 列表转换为二进制数据
        embedding_blob = pickle.dumps(embedding)
        
        cursor.execute(f'''
            UPDATE papers
            SET vector = ?
            WHERE primary_key = ?
        ''', (embedding_blob, paper_id))
        
        conn.commit()
        conn.close()

    def batch_generate_embedding(self, papers: List[Tuple[str, str, str]], 
                            batch_size: int = 16) -> Dict[str, List[float]]:
        """ 批量生成 embedding """
        paper_embeddings = {}
        
        for i in range(0, len(papers), batch_size):
            batch = papers[i:i + batch_size]
            # 将 title 和 sub_summary 拼接
            texts = [f"{title}\n{sub_summary}" for _, title, sub_summary in batch]
            
            # 生成 embedding
            embeddings = self.embedding_model.embedding(texts, vector_length=2048)
            
            # 将结果与 paper_id 对应存储
            for j, (paper_id, _, _) in enumerate(batch):
                if j < len(embeddings):  # 确保有对应的 embedding
                    paper_embeddings[paper_id] = embeddings[j]
        
        return paper_embeddings

    def process_papers_embedding(self, batch_size: int = 16) -> None:
        """处理数据库中所有没有 embedding 的文章"""
        # 获取所有没有 embedding 的文章
        papers = self.get_papers_without_embedding()
        print(f"找到 {len(papers)} 篇需要处理的文章")
        
        # 批量生成 embedding
        for i in tqdm(range(0, len(papers), batch_size)):
            batch = papers[i:i + batch_size]
            paper_embeddings = self.batch_generate_embedding(batch, batch_size)
            
            # 更新数据库
            for paper_id, embedding in paper_embeddings.items():
                self.update_paper_embedding(paper_id, embedding)

    # =============== 三、将数据库中数据转换为 zilliz 导入格式 ===============
    def get_papers_by_like(self, like_str: str = '2025-01-%'):
        """根据 like 信息筛选数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 初始化搜索列名
        search_columns = ['primary_key', 'vector', 'title', 'authors', 'summary', 'published', 'category', 'pdf_url', 'sub_summary', 'source', 'doi']
        
        cursor.execute(f'''
            SELECT {', '.join(search_columns)}
            FROM papers
            WHERE vector IS NOT NULL
            AND published like '{like_str}'
        ''')
        
        papers = cursor.fetchall()
        conn.close()

        # 将搜索结果中每一个 paper 的 embedding 字段从二进制数据转换为 list 类型
        # 同时将 paper 转换为 map 类型，方便后续处理
        papers = [dict(zip(search_columns, paper)) for paper in papers]

        return papers

    def trans_to_zilliz(self, papers) -> list[dict]:
        # 转换为 zilliz 格式
        rows = []
        for paper in papers:
            try:
                # 获取向量表示
                # 这里的 paper['embedding'] 是一个 BLOB 类型的二进制数据
                # 转换为 list 类型的向量
                
                # 构建数据字典
                # 根据 Milvus schema 构建数据字典
                data_dict = {
                    "primary_key": str(paper.get('primary_key', ''))[:1000],  # VARCHAR(1000)
                    "vector": pickle.loads(paper.get('vector')),  # FLOAT_VECTOR(1024)
                    "title": str(paper.get('title', ''))[:500],  # VARCHAR(500)
                    "authors": json.loads(paper.get('authors', []))[:500],  # Array<VARCHAR(100)>[500]
                    "summary": str(paper.get('summary', ''))[:4900],  # VARCHAR(4900)
                    "published": str(paper.get('published', ''))[:100],  # VARCHAR(100)
                    "category": str(paper.get('category', ''))[:100],  # VARCHAR(100)
                    "pdf_url": str(paper.get('pdf_url', ''))[:200],  # VARCHAR(200)
                    "sub_summary": str(paper.get('sub_summary', ''))[:1900],  # VARCHAR(1900)
                    "source": str(paper.get('source', ''))[:100],  # VARCHAR(100)
                    "doi": str(paper.get('doi', ''))[:200]  # VARCHAR(200)
                }
                rows.append(data_dict)
            except Exception as e:
                print(f"处理文档 {paper.get('primary_key', '')} 时出错: {e}")
                continue

        return rows

    def main_trans_to_zilliz(self, year: int, sources: list[str] = [], save_type: str = 'month'):
        # 检查文件夹
        if not os.path.exists(self.zilliz_path):
            os.makedirs(self.zilliz_path)

        def save_to_json(rows: list[dict], file_name: str):
            if len(rows) == 0:
                print(f"没有数据，跳过保存")
                return
            path = f'{self.zilliz_path}/{file_name}'
            json_file = {'rows': rows}
            with open(path, 'w') as f:
                json.dump(json_file, f)
            print(f"数据已保存，共 {len(rows)} 条记录")

        if save_type == 'month':
            # 1. 12 个月分开存储
            for i in tqdm(range(12)):
                # 按照月份获取数据
                like_str = f'{year}-{0 if i+1 < 10 else ""}{i+1}-%'
                papers = self.get_papers_by_like(like_str)
                if len(papers) == 0: continue

                # 根据 sources 筛选数据
                if len(sources) > 0:
                    papers = [paper for paper in papers if paper['source'].lower() in sources]
                    if len(papers) == 0: continue

                rows = self.trans_to_zilliz(papers)

                # 使用 save_to_json 函数存储
                save_to_json(rows, f'papers_y{year}_m{i}.json')

        else:
            # 2. 按照参数对应进行等分存储
            # 2.1 获取所有数据
            all_rows = []
            for i in range(12):
                # 按照月份获取数据
                like_str = f'{year}-{0 if i+1 < 10 else ""}{i+1}-%'
                papers = self.get_papers_by_like(like_str)
                if len(papers) == 0: continue

                # 根据 sources 筛选数据
                if len(sources) > 0:
                    papers = [paper for paper in papers if paper['source'].lower() in sources]
                    if len(papers) == 0: continue

                rows = self.trans_to_zilliz(papers)
                all_rows.extend(rows)

            # 2.2 按照参数对应进行等分存储
            if save_type == 'year':
                save_to_json(all_rows, f'papers_y{year}_all.json')
            elif save_type == 'half':
                save_to_json(all_rows[:len(all_rows)//2], f'papers_y{year}_2_1.json')
                save_to_json(all_rows[len(all_rows)//2:], f'papers_y{year}_2_2.json')
            elif save_type == 'quarter':
                save_to_json(all_rows[:len(all_rows)//4], f'papers_y{year}_4_1.json')
                save_to_json(all_rows[len(all_rows)//4:len(all_rows)//2], f'papers_y{year}_4_2.json')
                save_to_json(all_rows[len(all_rows)//2:len(all_rows)//4*3], f'papers_y{year}_4_3.json')
                save_to_json(all_rows[len(all_rows)//4*3:], f'papers_y{year}_4_4.json')
            else:
                raise ValueError(f"save_type 参数错误: {save_type}")

def deal_arxiv_paper(year: int):
    # 1.2 初始化路径
    root_path_list = ['data', 'paper_arxiv']
    db_path = os.path.join(ROOT_DIR, *root_path_list, 'db', f'papers_{year}.db')
    bom_path = os.path.join(ROOT_DIR, *root_path_list, 'paper')
    zilliz_path = os.path.join(ROOT_DIR, *root_path_list, 'zilliz')

    # 1.3 初始化模型
    embedding_model=BaseEmbedding(**ModelSelector.sc_qwen3_embedding_4b) # 初始化 embedding 模型

    # 1.4 初始化 PaperDeal 类
    paper_deal = BomDeal(year=year,
                           db_path=db_path,
                           bom_path=bom_path,
                           zilliz_path=zilliz_path,
                           # init model
                           embedding_model=embedding_model)

    # 3. 创建数据库
    paper_deal.create_database()

    # 4. 插入论文
    print(f"1. 插入论文到数据库中")
    paper_deal.main_create_insert()

    # 5. 更新 sub_summary (可重复执行)
    print(f"2. 更新 sub_summary")
    paper_deal.generate_sub_summaries()

    # 6. 更新 embedding  (可重复执行)
    print(f"3. 更新 embedding")
    paper_deal.process_papers_embedding(64)
    
    # 7. 转换为 zilliz 格式 (可重复执行)
    print(f"4. 转换为 zilliz 格式")
    save_type = 'year'
    if year > 2017: save_type = 'half'
    if year > 2020: save_type = 'quarter'
    paper_deal.main_trans_to_zilliz(year=year, save_type=save_type)


if __name__ == "__main__":
    pass