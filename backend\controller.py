import os
import uvicorn

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI()
origins = ["http://localhost:5173"]  # Vite 默认端口
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins, allow_credentials=True,
    allow_methods=["*"], allow_headers=["*"],
)

@app.get("/hello")
def hello():
    return {"message": "world"}


if __name__ == "__main__":
    file_path = os.path.basename(__file__)
    module_name = file_path[:-3]  # 移除 .py 后缀
    uvicorn.run(
        f"{module_name}:app",
        host="0.0.0.0",
        port=8848,
        reload=False,
        log_level="debug"  # 启用日志输出
    )