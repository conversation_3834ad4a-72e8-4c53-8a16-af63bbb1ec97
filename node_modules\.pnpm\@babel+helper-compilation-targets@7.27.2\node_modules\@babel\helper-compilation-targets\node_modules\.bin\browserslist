#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/project/lab/bom_match_demo/node_modules/.pnpm/browserslist@4.25.3/node_modules/browserslist/node_modules:/mnt/e/project/lab/bom_match_demo/node_modules/.pnpm/browserslist@4.25.3/node_modules:/mnt/e/project/lab/bom_match_demo/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/project/lab/bom_match_demo/node_modules/.pnpm/browserslist@4.25.3/node_modules/browserslist/node_modules:/mnt/e/project/lab/bom_match_demo/node_modules/.pnpm/browserslist@4.25.3/node_modules:/mnt/e/project/lab/bom_match_demo/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../browserslist@4.25.3/node_modules/browserslist/cli.js" "$@"
else
  exec node  "$basedir/../../../../../../browserslist@4.25.3/node_modules/browserslist/cli.js" "$@"
fi
