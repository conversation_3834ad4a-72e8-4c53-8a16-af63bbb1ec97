# Get your OpenAI API Key here: https://platform.openai.com/account/api-keys
OPENAI_API_KEY_CD="sk-kz9TVi69gKVBNS4cqSNWYopVrjeQqjXJmHl8SLLuS8KwbCcJ"
OPENAI_API_KEY_AZ="sk-LtQArr0R8j55ixMKbmlyQk4QelR1QWCxBW6ZZ9Fuzh5URgNl"
OPENAI_API_KEY_O1="sk-h2NVCeCRmKm69aNqhN8CPF0Dwfbt3Nz9kWum3pYkPyyEkFh7"
OPENAI_API_KEY_DF="sk-a0E2vfuwNZldpxslaZlozmAU9pNb3a2CyjoRPJM8EfiwmkUR"
OPENAI_API_KEY_SC="sk-ladpngumcpvzeycwdgxgbvhdfnfmwlnkxrhctrdsttzwoxxf"
OPENAI_API_KEY_GLM="19bab48ee2d5acf57096815a8dede9b1.UnpVRWj7Y8OMFe0f"

OPENAI_BASE_URL="https://api.wlai.vip/v1"
OPENAI_BASE_URL_SC="https://api.siliconflow.cn/v1"
OPENAI_BASE_URL_GLM="https://open.bigmodel.cn/api/paas/v4/"

# ===================

# Get your OpenAI API Key here: https://platform.openai.com/account/api-keys
API_KEY_DS="sk-349bfe9f28d845b193d992e5cd8b0bb7" # DeepSeek 官方服务
API_KEY_YW="sk-kz9TVi69gKVBNS4cqSNWYopVrjeQqjXJmHl8SLLuS8KwbCcJ" # 云雾
API_KEY_OP="sk_x6tBZ_Rg-pE2n4jrKAZQbfsjXAPWq5YKPGtOP-KSZv8" # 欧派云
API_KEY_SC="sk-ladpngumcpvzeycwdgxgbvhdfnfmwlnkxrhctrdsttzwoxxf" # 硅基流动
API_KEY_DB="9f0ddc38-2613-48a1-ae53-014c07ddfe36" # 豆包
API_KEY_AL="sk-74dd34b8046c4f5db3da057a27c4a701" # 通义千问
API_KEY_GLM="19bab48ee2d5acf57096815a8dede9b1.UnpVRWj7Y8OMFe0f" # 智谱

API_KEY_YW_Claude="sk-p6PprHnjEHJIgpX8E37363F36b8e44D99c51A1B3EeE5F100" # 云雾 Claude 系列模型
API_KEY_YW_GEMINI="sk-Mo5K9m2hKXjV1DeGeBAIzXLZFxxiOTvSwUoemKmfMXdmE9Bs" # 云雾 Gemini 系列模型
API_KEY_YW_GPT="sk-5dwG5NvQjafHFNiuFe07D8272d12467b88A02cF1Fb3890A8" # 云雾 GPT 系列模型 

BASE_URL_DS="https://api.deepseek.com"
BASE_URL_YW="https://yunwu.ai/v1"
BASE_URL_SC="https://api.siliconflow.cn/v1"
BASE_URL_OP="https://api.ppinfra.com/v3/openai"
BASE_URL_DB="https://ark.cn-beijing.volces.com/api/v3"
BASE_URL_AL="https://dashscope.aliyuncs.com/compatible-mode/v1"
BASE_URL_GLM="https://open.bigmodel.cn/api/paas/v4"
BASE_URL_GLM_EMBEDDING="https://open.bigmodel.cn/api/paas/v4/embeddings"

# zilliz connect
zilliz_uri_bom="https://in03-d5e93c0398582a0.serverless.ali-cn-hangzhou.cloud.zilliz.com.cn"
zilliz_token="fd07cc6f0be378fd08866edbdb57e3f9bde1b4d612f8dc73da850e45fc7e9666d057b11f2ba93189fcc2c2a6f42bd36ba9b2ccb0"