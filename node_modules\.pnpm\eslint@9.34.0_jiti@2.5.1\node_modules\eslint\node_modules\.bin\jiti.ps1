#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="E:\project\lab\bom_match_demo\node_modules\.pnpm\jiti@2.5.1\node_modules\jiti\lib\node_modules;E:\project\lab\bom_match_demo\node_modules\.pnpm\jiti@2.5.1\node_modules\jiti\node_modules;E:\project\lab\bom_match_demo\node_modules\.pnpm\jiti@2.5.1\node_modules;E:\project\lab\bom_match_demo\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/e/project/lab/bom_match_demo/node_modules/.pnpm/jiti@2.5.1/node_modules/jiti/lib/node_modules:/mnt/e/project/lab/bom_match_demo/node_modules/.pnpm/jiti@2.5.1/node_modules/jiti/node_modules:/mnt/e/project/lab/bom_match_demo/node_modules/.pnpm/jiti@2.5.1/node_modules:/mnt/e/project/lab/bom_match_demo/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../../../../../jiti@2.5.1/node_modules/jiti/lib/jiti-cli.mjs" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../../../../../jiti@2.5.1/node_modules/jiti/lib/jiti-cli.mjs" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../../../../../jiti@2.5.1/node_modules/jiti/lib/jiti-cli.mjs" $args
  } else {
    & "node$exe"  "$basedir/../../../../../jiti@2.5.1/node_modules/jiti/lib/jiti-cli.mjs" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
